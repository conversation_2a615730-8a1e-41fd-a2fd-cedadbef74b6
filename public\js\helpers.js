/**
 * Universal Helper Functions for RX-Info Application
 * Contains reusable JavaScript functions for common operations
 */

/**
 * Universal Delete Function
 * @param {Object} options - Configuration object
 * @param {string} options.id - The ID of the item to delete
 * @param {string} options.url - The delete URL endpoint
 * @param {string} options.itemName - Name of the item being deleted (e.g., 'drug', 'user', 'category')
 * @param {Object} options.table - DataTable instance to reload after deletion
 * @param {string} [options.confirmTitle] - Custom confirmation title
 * @param {string} [options.confirmText] - Custom confirmation text
 * @param {string} [options.loadingTitle] - Custom loading title
 * @param {string} [options.loadingText] - Custom loading text
 * @param {string} [options.successTitle] - Custom success title
 * @param {string} [options.successMessage] - Custom success message
 * @param {Function} [options.onSuccess] - Custom success callback
 * @param {Function} [options.onError] - Custom error callback
 */
function universalDelete(options) {
    // Validate required parameters
    if (!options.id || !options.url || !options.itemName) {
        console.error('universalDelete: Missing required parameters (id, url, itemName)');
        return;
    }

    // Default configuration
    const config = {
        confirmTitle: options.confirmTitle || 'Are you sure?',
        confirmText: options.confirmText || "You won't be able to revert this action!",
        loadingTitle: options.loadingTitle || 'Deleting...',
        loadingText: options.loadingText || `Please wait while we delete the ${options.itemName}.`,
        successTitle: options.successTitle || 'Deleted!',
        successMessage: options.successMessage || `${options.itemName.charAt(0).toUpperCase() + options.itemName.slice(1)} has been deleted successfully.`,
        ...options
    };

    Swal.fire({
        title: config.confirmTitle,
        text: config.confirmText,
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#d33',
        cancelButtonColor: '#3085d6',
        confirmButtonText: 'Yes, delete it!',
        cancelButtonText: 'Cancel'
    }).then((result) => {
        if (result.isConfirmed) {
            // Show loading
            Swal.fire({
                title: config.loadingTitle,
                text: config.loadingText,
                allowOutsideClick: false,
                allowEscapeKey: false,
                showConfirmButton: false,
                didOpen: () => {
                    Swal.showLoading();
                }
            });

            // Perform delete request
            $.ajax({
                url: config.url.replace(':id', config.id),
                type: 'DELETE',
                headers: {
                    'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                },
                success: function(response) {
                    const successMessage = response.message || config.successMessage;

                    Swal.fire({
                        icon: 'success',
                        title: config.successTitle,
                        text: successMessage,
                        timer: 2000,
                        showConfirmButton: false
                    });

                    // Custom success callback or default table reload
                    if (config.onSuccess && typeof config.onSuccess === 'function') {
                        config.onSuccess(response);
                    } else if (config.table) {
                        config.table.ajax.reload(null, false);
                    }
                },
                error: function(xhr) {
                    let errorMessage = `An error occurred while deleting the ${config.itemName}.`;
                    if (xhr.responseJSON && xhr.responseJSON.message) {
                        errorMessage = xhr.responseJSON.message;
                    }

                    Swal.fire({
                        icon: 'error',
                        title: 'Error!',
                        text: errorMessage
                    });

                    // Custom error callback
                    if (config.onError && typeof config.onError === 'function') {
                        config.onError(xhr);
                    }
                }
            });
        }
    });
}

/**
 * Universal Refresh Table Function
 * @param {Object} table - DataTable instance
 * @param {string} [message] - Custom success message
 */
function refreshTable(table, message = 'Table data has been refreshed.') {
    if (table && typeof table.ajax !== 'undefined') {
        table.ajax.reload(null, false);
        Swal.fire({
            icon: 'success',
            title: 'Refreshed!',
            text: message,
            timer: 1500,
            showConfirmButton: false
        });
    } else {
        console.error('refreshTable: Invalid table instance provided');
    }
}

/**
 * Universal Status Toggle Function
 * @param {Object} options - Configuration object
 * @param {string} options.id - The ID of the item
 * @param {string} options.url - The status toggle URL endpoint
 * @param {string} options.itemName - Name of the item
 * @param {Object} options.table - DataTable instance to reload
 * @param {string} [options.currentStatus] - Current status of the item
 */
function toggleStatus(options) {
    if (!options.id || !options.url || !options.itemName) {
        console.error('toggleStatus: Missing required parameters (id, url, itemName)');
        return;
    }

    const action = options.currentStatus === 'active' ? 'deactivate' : 'activate';

    Swal.fire({
        title: `${action.charAt(0).toUpperCase() + action.slice(1)} ${options.itemName}?`,
        text: `Are you sure you want to ${action} this ${options.itemName}?`,
        icon: 'question',
        showCancelButton: true,
        confirmButtonColor: '#3085d6',
        cancelButtonColor: '#d33',
        confirmButtonText: `Yes, ${action} it!`,
        cancelButtonText: 'Cancel'
    }).then((result) => {
        if (result.isConfirmed) {
            $.ajax({
                url: options.url.replace(':id', options.id),
                type: 'PATCH',
                headers: {
                    'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                },
                success: function(response) {
                    Swal.fire({
                        icon: 'success',
                        title: 'Success!',
                        text: response.message || `${options.itemName} status updated successfully.`,
                        timer: 2000,
                        showConfirmButton: false
                    });

                    if (options.table) {
                        options.table.ajax.reload(null, false);
                    }
                },
                error: function(xhr) {
                    let errorMessage = `An error occurred while updating the ${options.itemName} status.`;
                    if (xhr.responseJSON && xhr.responseJSON.message) {
                        errorMessage = xhr.responseJSON.message;
                    }

                    Swal.fire({
                        icon: 'error',
                        title: 'Error!',
                        text: errorMessage
                    });
                }
            });
        }
    });
}

/**
 * Show loading overlay
 * @param {string} [message] - Loading message
 */
function showLoading(message = 'Loading...') {
    Swal.fire({
        title: message,
        allowOutsideClick: false,
        allowEscapeKey: false,
        showConfirmButton: false,
        didOpen: () => {
            Swal.showLoading();
        }
    });
}

/**
 * Hide loading overlay
 */
function hideLoading() {
    Swal.close();
}

/**
 * Show success message
 * @param {string} title - Success title
 * @param {string} [text] - Success message
 * @param {number} [timer] - Auto close timer in milliseconds
 */
function showSuccess(title, text = '', timer = 2000) {
    Swal.fire({
        icon: 'success',
        title: title,
        text: text,
        timer: timer,
        showConfirmButton: false
    });
}

/**
 * Show error message
 * @param {string} title - Error title
 * @param {string} [text] - Error message
 */
function showError(title, text = '') {
    Swal.fire({
        icon: 'error',
        title: title,
        text: text
    });
}

/**
 * Universal DataTable Initialization Function
 * @param {Object} options - Configuration object
 * @param {string} options.tableId - The ID of the table element
 * @param {string} options.ajaxUrl - The AJAX URL for data
 * @param {Array} options.columns - Array of column definitions
 * @param {string} [options.itemName] - Name of the items (for empty messages)
 * @param {number} [options.pageLength] - Default page length
 * @param {Array} [options.lengthMenu] - Length menu options
 * @param {Array} [options.order] - Default ordering
 * @param {Object} [options.language] - Custom language settings
 * @param {string} [options.dom] - Custom DOM layout
 * @param {Function} [options.drawCallback] - Custom draw callback
 * @param {Function} [options.ajaxData] - Custom AJAX data function
 * @param {Function} [options.ajaxError] - Custom AJAX error handler
 * @param {boolean} [options.processing] - Show processing indicator
 * @param {boolean} [options.serverSide] - Enable server-side processing
 * @param {boolean} [options.responsive] - Enable responsive extension
 * @returns {Object} DataTable instance
 */
function initializeDataTable(options) {
    // Validate required parameters
    if (!options.tableId || !options.ajaxUrl || !options.columns) {
        console.error('initializeDataTable: Missing required parameters (tableId, ajaxUrl, columns)');
        return null;
    }

    // Default configuration
    const config = {
        processing: options.processing !== false, // Default true
        serverSide: options.serverSide !== false, // Default true
        responsive: options.responsive !== false, // Default true
        scrollX: options.scrollX || false, // Default false
        pageLength: options.pageLength || 25,
        lengthMenu: options.lengthMenu || [[10, 25, 50, 100, -1], [10, 25, 50, 100, "All"]],
        order: options.order || [[0, 'desc']],
        itemName: options.itemName || 'record',

        // AJAX configuration
        ajax: {
            url: options.ajaxUrl,
            type: 'GET',
            data: options.ajaxData || function(d) {
                // Default empty data function
                return d;
            },
            error: options.ajaxError || function(xhr, error, thrown) {
                console.error('DataTable AJAX Error:', error);
                Swal.fire({
                    icon: 'error',
                    title: 'Error Loading Data',
                    text: `Failed to load ${config.itemName} data. Please refresh the page.`,
                    confirmButtonText: 'Refresh Page'
                }).then((result) => {
                    if (result.isConfirmed) {
                        location.reload();
                    }
                });
            }
        },

        // Columns
        columns: options.columns,

        // DOM layout
        dom: options.dom || '<"row"<"col-sm-12 col-md-6"l><"col-sm-12 col-md-6"f>>' +
             '<"row"<"col-sm-12"tr>>' +
             '<"row"<"col-sm-12 col-md-5"i><"col-sm-12 col-md-7"p>>',

        // Language settings
        language: options.language || {
            processing: '<div class="d-flex justify-content-center"><div class="spinner-border text-primary" role="status"><span class="visually-hidden">Loading...</span></div></div>',
            emptyTable: `<div class="text-center py-4"><i class="fas fa-database fa-3x text-muted mb-3"></i><br><span class="text-muted">No ${config.itemName}s found</span></div>`,
            zeroRecords: '<div class="text-center py-4"><i class="fas fa-search fa-3x text-muted mb-3"></i><br><span class="text-muted">No matching records found</span></div>',
            lengthMenu: "Show _MENU_ entries",
            search: "Search:",
            paginate: {
                first: "First",
                last: "Last",
                next: "Next",
                previous: "Previous"
            },
            info: "Showing _START_ to _END_ of _TOTAL_ entries",
            infoEmpty: "Showing 0 to 0 of 0 entries",
            infoFiltered: "(filtered from _MAX_ total entries)"
        },

        // Draw callback
        drawCallback: options.drawCallback || function(settings) {
            // Reinitialize tooltips after each draw
            $('[title]').tooltip();

            // Add loading state management
            $(`#${options.tableId}`).removeClass('table-loading');
        }
    };

    // Initialize DataTable
    const table = $(`#${options.tableId}`).DataTable(config);

    // Add loading state when table is processing
    table.on('processing.dt', function(e, settings, processing) {
        if (processing) {
            $(`#${options.tableId}`).addClass('table-loading');
        } else {
            $(`#${options.tableId}`).removeClass('table-loading');
        }
    });

    // Initialize tooltips
    $('[title]').tooltip();

    return table;
}

/**
 * Get standard column definitions for common column types
 * @param {string} type - Column type ('index', 'actions', 'status', 'date', 'text')
 * @param {Object} [options] - Additional options for the column
 * @returns {Object} Column definition
 */
function getColumnDefinition(type, options = {}) {
    const definitions = {
        index: {
            data: 'DT_RowIndex',
            name: 'DT_RowIndex',
            orderable: false,
            searchable: false,
            className: 'text-center',
            ...options
        },
        actions: {
            data: 'actions',
            name: 'actions',
            orderable: false,
            searchable: false,
            className: 'text-center',
            ...options
        },
        status: {
            data: 'status',
            name: 'status',
            orderable: false,
            className: 'text-center',
            ...options
        },
        date: {
            className: 'text-center',
            ...options
        },
        text: {
            ...options
        }
    };

    return definitions[type] || options;
}

/**
 * Universal Image Preview Function
 * @param {Object} options - Configuration object
 * @param {string} options.inputId - ID of the file input element
 * @param {string} options.previewId - ID of the preview container
 * @param {string} options.imageId - ID of the preview image element
 * @param {Array} [options.allowedTypes] - Allowed file types
 * @param {number} [options.maxSize] - Maximum file size in bytes
 * @param {string} [options.maxWidth] - Maximum preview width
 * @param {string} [options.maxHeight] - Maximum preview height
 */
function initializeImagePreview(options) {
    // Validate required parameters
    if (!options.inputId || !options.previewId || !options.imageId) {
        console.error('initializeImagePreview: Missing required parameters (inputId, previewId, imageId)');
        return;
    }

    // Default configuration
    const config = {
        allowedTypes: options.allowedTypes || ['image/jpeg', 'image/jpg', 'image/png', 'image/gif'],
        maxSize: options.maxSize || (2 * 1024 * 1024), // 2MB default
        maxWidth: options.maxWidth || '200px',
        maxHeight: options.maxHeight || '200px',
        ...options
    };

    $(`#${config.inputId}`).on('change', function() {
        const file = this.files[0];
        if (file) {
            // Validate file type
            if (!config.allowedTypes.includes(file.type)) {
                Swal.fire({
                    icon: 'error',
                    title: 'Invalid File Type',
                    text: `Please select a valid image file (${config.allowedTypes.map(type => type.split('/')[1].toUpperCase()).join(', ')}).`
                });
                $(this).val('');
                $(`#${config.previewId}`).hide();
                return;
            }

            // Validate file size
            if (file.size > config.maxSize) {
                const sizeMB = (config.maxSize / (1024 * 1024)).toFixed(1);
                Swal.fire({
                    icon: 'error',
                    title: 'File Too Large',
                    text: `Please select an image smaller than ${sizeMB}MB.`
                });
                $(this).val('');
                $(`#${config.previewId}`).hide();
                return;
            }

            // Show preview
            const reader = new FileReader();
            reader.onload = function(e) {
                $(`#${config.imageId}`)
                    .attr('src', e.target.result)
                    .css({
                        'max-width': config.maxWidth,
                        'max-height': config.maxHeight
                    });
                $(`#${config.previewId}`).show();
            };
            reader.readAsDataURL(file);
        } else {
            $(`#${config.previewId}`).hide();
        }
    });
}

/**
 * Universal Form Submission Function
 * @param {Object} options - Configuration object
 * @param {string} options.formId - ID of the form element
 * @param {string} options.submitBtnId - ID of the submit button
 * @param {string} [options.successMessage] - Success message
 * @param {string} [options.redirectUrl] - URL to redirect after success
 * @param {boolean} [options.hasFileUpload] - Whether form has file uploads
 * @param {Function} [options.onSuccess] - Custom success callback
 * @param {Function} [options.onError] - Custom error callback
 * @param {Function} [options.beforeSubmit] - Callback before form submission
 */
function initializeFormSubmission(options) {
    // Validate required parameters
    if (!options.formId || !options.submitBtnId) {
        console.error('initializeFormSubmission: Missing required parameters (formId, submitBtnId)');
        return;
    }

    // Default configuration
    const config = {
        successMessage: options.successMessage || 'Data has been saved successfully.',
        hasFileUpload: options.hasFileUpload || false,
        ...options
    };

    $(`#${config.formId}`).on('submit', function(e) {
        e.preventDefault();

        // Execute before submit callback
        if (config.beforeSubmit && typeof config.beforeSubmit === 'function') {
            const shouldContinue = config.beforeSubmit();
            if (shouldContinue === false) return;
        }

        // Show loading state
        const submitBtn = $(`#${config.submitBtnId}`);
        submitBtn.attr('disabled', true);
        submitBtn.find('.indicator-label').hide();
        submitBtn.find('.indicator-progress').show();

        // Prepare form data
        const formData = config.hasFileUpload ? new FormData(this) : $(this).serialize();

        // AJAX configuration
        const ajaxConfig = {
            url: $(this).attr('action'),
            type: 'POST',
            data: formData,
            success: function(response) {
                if (config.onSuccess && typeof config.onSuccess === 'function') {
                    config.onSuccess(response);
                } else {
                    Swal.fire({
                        icon: 'success',
                        title: 'Success!',
                        text: config.successMessage,
                        timer: 2000,
                        showConfirmButton: false
                    }).then(() => {
                        if (config.redirectUrl) {
                            window.location.href = config.redirectUrl;
                        }
                    });
                }
            },
            error: function(xhr) {
                // Reset button state
                submitBtn.attr('disabled', false);
                submitBtn.find('.indicator-label').show();
                submitBtn.find('.indicator-progress').hide();

                if (config.onError && typeof config.onError === 'function') {
                    config.onError(xhr);
                } else {
                    handleFormErrors(xhr);
                }
            }
        };

        // Add file upload specific settings
        if (config.hasFileUpload) {
            ajaxConfig.processData = false;
            ajaxConfig.contentType = false;
        }

        $.ajax(ajaxConfig);
    });
}

/**
 * Handle form validation errors
 * @param {Object} xhr - XMLHttpRequest object
 */
function handleFormErrors(xhr) {
    if (xhr.status === 422) {
        // Validation errors
        const errors = xhr.responseJSON.errors;

        // Clear previous error states
        $('.is-invalid').removeClass('is-invalid');
        $('.invalid-feedback').remove();

        // Display validation errors
        $.each(errors, function(field, messages) {
            const input = $(`[name="${field}"]`);
            input.addClass('is-invalid');
            input.after(`<div class="invalid-feedback">${messages[0]}</div>`);
        });

        Swal.fire({
            icon: 'error',
            title: 'Validation Error',
            text: 'Please check the form for errors and try again.',
            confirmButtonText: 'OK'
        });
    } else {
        // Server error
        Swal.fire({
            icon: 'error',
            title: 'Error',
            text: xhr.responseJSON?.message || 'An error occurred while processing your request.',
            confirmButtonText: 'OK'
        });
    }
}

/**
 * Initialize auto-resize textareas
 * @param {string} [selector] - CSS selector for textareas (default: 'textarea')
 */
function initializeAutoResizeTextareas(selector = 'textarea') {
    $(selector).each(function() {
        this.setAttribute('style', 'height:' + (this.scrollHeight) + 'px;overflow-y:hidden;');
    }).on('input', function() {
        this.style.height = 'auto';
        this.style.height = (this.scrollHeight) + 'px';
    });
}

/**
 * Initialize character counter for textareas
 * @param {string} [selector] - CSS selector for textareas (default: 'textarea[maxlength]')
 */
function initializeCharacterCounter(selector = 'textarea[maxlength]') {
    $(selector).on('input', function() {
        const maxLength = $(this).attr('maxlength');
        if (maxLength) {
            const currentLength = $(this).val().length;
            const counter = $(this).siblings('.char-counter');
            if (counter.length === 0) {
                $(this).after(`<div class="char-counter text-muted fs-7 mt-1">${currentLength}/${maxLength} characters</div>`);
            } else {
                counter.text(`${currentLength}/${maxLength} characters`);
            }
        }
    });
}

/**
 * Initialize unsaved changes warning
 * @param {Object} options - Configuration object
 * @param {string} options.formId - ID of the form to monitor
 * @param {string} options.exitSelector - CSS selector for exit links/buttons
 * @param {string} [options.warningTitle] - Custom warning title
 * @param {string} [options.warningText] - Custom warning text
 */
function initializeUnsavedChangesWarning(options) {
    if (!options.formId || !options.exitSelector) {
        console.error('initializeUnsavedChangesWarning: Missing required parameters (formId, exitSelector)');
        return;
    }

    const config = {
        warningTitle: options.warningTitle || 'Are you sure?',
        warningText: options.warningText || 'You have unsaved changes. Do you want to leave without saving?',
        ...options
    };

    $(document).on('click', config.exitSelector, function(e) {
        const form = $(`#${config.formId}`);
        let hasData = false;

        // Check if form has any data
        form.find('input, textarea, select').each(function() {
            if ($(this).val() && $(this).val().trim() !== '') {
                hasData = true;
                return false;
            }
        });

        if (hasData) {
            e.preventDefault();
            const href = $(this).attr('href');

            Swal.fire({
                title: config.warningTitle,
                text: config.warningText,
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#d33',
                cancelButtonColor: '#3085d6',
                confirmButtonText: 'Yes, leave',
                cancelButtonText: 'Stay'
            }).then((result) => {
                if (result.isConfirmed && href) {
                    window.location.href = href;
                }
            });
        }
    });
}

/**
 * Initialize form with all common enhancements
 * @param {Object} options - Configuration object
 * @param {string} options.formId - ID of the form
 * @param {string} options.submitBtnId - ID of the submit button
 * @param {string} [options.imageInputId] - ID of image input (if any)
 * @param {string} [options.imagePreviewId] - ID of image preview container
 * @param {string} [options.previewImageId] - ID of preview image element
 * @param {string} [options.exitSelector] - Selector for exit links
 * @param {string} [options.successMessage] - Success message
 * @param {string} [options.redirectUrl] - Redirect URL after success
 * @param {boolean} [options.hasFileUpload] - Whether form has file uploads
 * @param {Function} [options.onSuccess] - Custom success callback
 * @param {Function} [options.onError] - Custom error callback
 */
function initializeEnhancedForm(options) {
    if (!options.formId || !options.submitBtnId) {
        console.error('initializeEnhancedForm: Missing required parameters (formId, submitBtnId)');
        return;
    }

    // Initialize form submission
    initializeFormSubmission({
        formId: options.formId,
        submitBtnId: options.submitBtnId,
        successMessage: options.successMessage,
        redirectUrl: options.redirectUrl,
        hasFileUpload: options.hasFileUpload,
        onSuccess: options.onSuccess,
        onError: options.onError
    });

    // Initialize image preview if specified
    if (options.imageInputId && options.imagePreviewId && options.previewImageId) {
        initializeImagePreview({
            inputId: options.imageInputId,
            previewId: options.imagePreviewId,
            imageId: options.previewImageId
        });
    }

    // Initialize auto-resize textareas
    initializeAutoResizeTextareas();

    // Initialize character counter
    initializeCharacterCounter();

    // Initialize unsaved changes warning if exit selector provided
    if (options.exitSelector) {
        initializeUnsavedChangesWarning({
            formId: options.formId,
            exitSelector: options.exitSelector
        });
    }

    // Initialize tooltips
    $('[data-bs-toggle="tooltip"]').tooltip();

    // Initialize Select2 for drug dropdowns if they exist
    initializeDrugSelect2();
}

/**
 * Initialize Select2 for drug dropdowns with search functionality
 * @param {string} [selector] - CSS selector for drug select elements (default: 'select[name="drug_id"]')
 * @param {Object} [options] - Additional Select2 options
 */
function initializeDrugSelect2(selector = 'select[name="drug_id"]', options = {}) {
    const defaultOptions = {
        theme: 'bootstrap-5',
        width: '100%',
        placeholder: 'Select Drug',
        allowClear: true,
        searchInputPlaceholder: 'Search for a drug...',
        language: {
            noResults: function() {
                return "No drugs found";
            },
            searching: function() {
                return "Searching...";
            }
        }
    };

    const finalOptions = { ...defaultOptions, ...options };

    // Only initialize if the selector exists on the page
    if ($(selector).length > 0) {
        $(selector).select2(finalOptions);
    }
}

/**
 * Handle notification box toggle + outside click
 */
function handleNotificationBox(event) {
    const box = document.getElementById("notificationBox");
    const button = document.querySelector(".btn.btn-icon"); // your bell button

    // If the click is on the button -> toggle box
    if (button.contains(event.target)) {
        box.classList.toggle("d-none");
        return;
    }

    // If box is open and click happens outside -> close it
    if (!box.classList.contains("d-none") && !box.contains(event.target)) {
        box.classList.add("d-none");
    }
}

/**
 * Handle notification box toggle + outside click
 */
function handleNotificationBox(event) {
    const box = document.getElementById("notificationBox");
    const button = document.getElementById("notificationButton");

    // If click is on button -> toggle box
    if (button.contains(event.target)) {
        box.classList.toggle("d-none");
        return;
    }

    // If box is open and click happens outside -> close it
    if (!box.classList.contains("d-none") && !box.contains(event.target)) {
        box.classList.add("d-none");
    }
}
// Attach once on document
document.addEventListener("click", handleNotificationBox);


