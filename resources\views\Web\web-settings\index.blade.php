@extends('Layouts.app')

@section('title', 'SamRx | Web Settings')

@section('content')
    <div class="d-flex flex-column flex-column-fluid">
        <div id="kt_app_content" class="app-content flex-column-fluid">
            <div id="kt_app_content_container" class="app-container container-fluid">
                <!-- Page Header -->
                <div class="row mt-5 mb-5">
                    <div class="col-12">
                        <div class="d-flex align-items-center justify-content-between">
                            <div><h1 class="page-heading d-flex text-dark fw-bold fs-3 flex-column justify-content-center my-0">Web Settings Management<span class="page-desc text-muted fs-7 fw-semibold pt-1">Manage and organize web settings information</span></h1></div>
                            <div><a href="{{ route('web-settings.create') }}" class="btn btn-primary"><i class="fas fa-plus"></i> Add New Web Setting</a></div>
                        </div>
                    </div>
                </div>

                <!-- Main Content -->
                <div class="row g-5 g-xl-10">
                    <div class="col-xl-12">
                        <div class="card card-flush">
                            <div class="card-body pt-0">
                                <div class="table-responsive">
                                    <table class="table align-middle table-row-dashed fs-6 gy-5" id="web-settings-table">
                                        <thead>
                                            <tr class="text-start text-muted fw-bold fs-7 text-uppercase gs-0">
                                                <th class="min-w-50px">#</th>
                                                <th class="min-w-150px">Key</th>
                                                <th class="min-w-150px">Value</th>
                                                <th class="min-w-100px">Status</th>
                                                <th class="min-w-125px">Created At</th>
                                                <th class="text-end min-w-100px">Actions</th>
                                            </tr>
                                        </thead>
                                        <tbody class="text-gray-600 fw-semibold">
                                            <!-- Data will be loaded via AJAX -->
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@section('scripts')
    <script>
        $(document).ready(function() {
            // Define columns for web settings table
            const columns = [
                getColumnDefinition('index'),
                { data: 'key', name: 'key' },
                { data: 'value', name: 'value' },
                getColumnDefinition('status', { data: 'status', name: 'status' }),
                getColumnDefinition('date', { data: 'created_at', name: 'created_at' }),
                getColumnDefinition('actions')
            ];

            // Initialize DataTable using helper function
            let table = initializeDataTable({
                tableId: 'web-settings-table',
                ajaxUrl: '{{ route("web-settings.index") }}',
                columns: columns,
                itemName: 'web setting',
                order: [[4, 'desc']], // Order by created_at desc
                responsive: false, // Disable responsive feature to show all columns
                scrollX: true, // Enable horizontal scrolling
                language: {
                    emptyTable: '<div class="text-center py-4"><i class="fas fa-cog fa-3x text-muted mb-3"></i><br><span class="text-muted">No web settings found</span></div>'
                }
            });

            // Delete functionality using universal function
            $(document).on('click', '.delete-web-settings', function() {
                const webSettingsId = $(this).data('id');
                universalDelete({id: webSettingsId, url: '{{ route("web-settings.destroy", ":id") }}', itemName: 'web setting', table: table});
            });
        });
    </script>
@endsection
